-- Migration: Grading Management System
-- Description: Create grading assignments, records, and AI grading tables
-- Created: 2025-01-16

-- 阅卷分配表
CREATE TABLE IF NOT EXISTS grading_assignments (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id           UUID REFERENCES exams ON DELETE CASCADE,
    question_id       UUID NOT NULL,
    student_id        UUID REFERENCES students ON DELETE CASCADE,
    grader_user_id    UUID REFERENCES public.users,
    assignment_type   VARCHAR(20) CHECK (assignment_type IN ('manual', 'ai', 'hybrid')),
    assignment_method VARCHAR(20) CHECK (assignment_method IN ('by_quantity', 'by_difficulty', 'by_subject', 'random')),
    priority_level    INTEGER DEFAULT 3 CHECK (priority_level BETWEEN 1 AND 5),
    assigned_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at        TIMESTAMP WITH TIME ZONE,
    completed_at      TIMESTAMP WITH TIME ZONE,
    status            VARCHAR(20) DEFAULT 'assigned' CHECK (status IN ('assigned', 'in_progress', 'completed', 'paused', 'cancelled')),
    estimated_time    INTEGER,
    actual_time       INTEGER,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 阅卷分配索引
CREATE INDEX IF NOT EXISTS idx_grading_assignments_exam ON grading_assignments (exam_id);
CREATE INDEX IF NOT EXISTS idx_grading_assignments_grader ON grading_assignments (grader_user_id);
CREATE INDEX IF NOT EXISTS idx_grading_assignments_status ON grading_assignments (status);
CREATE INDEX IF NOT EXISTS idx_grading_assignments_question ON grading_assignments (question_id);

-- 阅卷记录表
CREATE TABLE IF NOT EXISTS grading_records (
    id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id        UUID REFERENCES exams ON DELETE CASCADE,
    student_id     UUID REFERENCES students ON DELETE CASCADE,
    question_id    UUID NOT NULL,
    grader_user_id UUID REFERENCES public.users,
    original_score NUMERIC(5, 2),
    final_score    NUMERIC(5, 2),
    grading_method VARCHAR(20) CHECK (grading_method IN ('manual', 'ai', 'hybrid')),
    grading_time   INTEGER,
    grading_notes  TEXT,
    is_reviewed    BOOLEAN DEFAULT FALSE,
    reviewed_by    UUID REFERENCES public.users,
    reviewed_at    TIMESTAMP WITH TIME ZONE,
    created_at     TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at     TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 阅卷记录索引
CREATE INDEX IF NOT EXISTS idx_grading_records_exam ON grading_records (exam_id);
CREATE INDEX IF NOT EXISTS idx_grading_records_student ON grading_records (student_id);
CREATE INDEX IF NOT EXISTS idx_grading_records_grader ON grading_records (grader_user_id);
CREATE INDEX IF NOT EXISTS idx_grading_records_question ON grading_records (question_id);
CREATE INDEX IF NOT EXISTS idx_grading_records_reviewed ON grading_records (is_reviewed);

-- 题块阅卷记录表
CREATE TABLE IF NOT EXISTS card_block_grading_records (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id          UUID REFERENCES exams ON DELETE CASCADE,
    student_id       UUID REFERENCES students ON DELETE CASCADE,
    card_block_id    UUID REFERENCES answer_card_blocks ON DELETE CASCADE,
    grader_user_id   UUID REFERENCES public.users,
    raw_score        NUMERIC(5, 2) NOT NULL,
    adjusted_score   NUMERIC(5, 2),
    grading_method   VARCHAR(20) CHECK (grading_method IN ('manual', 'ai', 'hybrid')),
    confidence_score NUMERIC(3, 2),
    grading_notes    TEXT,
    quality_level    VARCHAR(20) CHECK (quality_level IN ('excellent', 'good', 'fair', 'poor')),
    is_reviewed      BOOLEAN DEFAULT FALSE,
    reviewed_by      UUID REFERENCES public.users,
    reviewed_at      TIMESTAMP WITH TIME ZONE,
    grading_duration INTEGER,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题块阅卷记录索引
CREATE INDEX IF NOT EXISTS idx_card_block_grading_records_exam ON card_block_grading_records (exam_id);
CREATE INDEX IF NOT EXISTS idx_card_block_grading_records_student ON card_block_grading_records (student_id);
CREATE INDEX IF NOT EXISTS idx_card_block_grading_records_block ON card_block_grading_records (card_block_id);
CREATE INDEX IF NOT EXISTS idx_card_block_grading_records_grader ON card_block_grading_records (grader_user_id);

-- AI阅卷记录表
CREATE TABLE IF NOT EXISTS ai_grading_records (
    id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id             UUID REFERENCES exams ON DELETE CASCADE,
    student_id          UUID REFERENCES students ON DELETE CASCADE,
    question_id         UUID NOT NULL,
    ai_agent_id         VARCHAR(100) NOT NULL,
    ai_model_version    VARCHAR(50) NOT NULL,
    ai_model_result     JSONB NOT NULL,
    confidence_score    NUMERIC(3, 2),
    processing_time     INTEGER,
    error_message       TEXT,
    reviewed_by_human   BOOLEAN DEFAULT FALSE,
    human_reviewer_id   UUID REFERENCES public.users,
    human_review_result JSONB,
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI阅卷记录索引
CREATE INDEX IF NOT EXISTS idx_ai_grading_records_exam ON ai_grading_records (exam_id);
CREATE INDEX IF NOT EXISTS idx_ai_grading_records_student ON ai_grading_records (student_id);
CREATE INDEX IF NOT EXISTS idx_ai_grading_records_question ON ai_grading_records (question_id);
CREATE INDEX IF NOT EXISTS idx_ai_grading_records_agent ON ai_grading_records (ai_agent_id);

-- 表注释
COMMENT ON TABLE grading_assignments IS '阅卷分配表 - 管理阅卷任务的分配';
COMMENT ON COLUMN grading_assignments.assignment_type IS '分配类型：manual-人工，ai-AI，hybrid-混合';
COMMENT ON COLUMN grading_assignments.priority_level IS '优先级：1-5，数字越小优先级越高';

COMMENT ON TABLE grading_records IS '阅卷记录表 - 记录具体的阅卷结果';
COMMENT ON TABLE card_block_grading_records IS '题块阅卷记录表 - 记录题块级别的阅卷结果';
COMMENT ON TABLE ai_grading_records IS 'AI阅卷记录表 - 记录AI阅卷的详细信息';
