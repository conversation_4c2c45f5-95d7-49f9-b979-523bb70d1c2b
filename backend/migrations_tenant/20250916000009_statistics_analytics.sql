-- Migration: Statistics and Analytics System
-- Description: Create statistical analysis and reporting tables
-- Created: 2025-01-16

-- 阅卷统计表
CREATE TABLE IF NOT EXISTS grading_statistics (
    id                 UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id            UUID REFERENCES exams ON DELETE CASCADE,
    question_id        UUID NOT NULL,
    question_type      VARCHAR(20) NOT NULL,
    grader_user_id     UUID REFERENCES public.users,
    total_papers       INTEGER DEFAULT 0,
    avg_score          NUMERIC(5, 2),
    min_score          NUMERIC(5, 2),
    max_score          NUMERIC(5, 2),
    score_distribution JSONB,
    consistency_score  NUMERIC(3, 2),
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 阅卷统计索引
CREATE INDEX IF NOT EXISTS idx_grading_statistics_exam ON grading_statistics (exam_id);
CREATE INDEX IF NOT EXISTS idx_grading_statistics_question ON grading_statistics (question_id);
CREATE INDEX IF NOT EXISTS idx_grading_statistics_grader ON grading_statistics (grader_user_id);

-- 阅卷控制状态表
CREATE TABLE IF NOT EXISTS grading_control_states (
    id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id        UUID REFERENCES exams ON DELETE CASCADE,
    question_id    UUID,
    grader_user_id UUID REFERENCES public.users,
    control_level  VARCHAR(20) CHECK (control_level IN ('global', 'public_resource', 'grader')),
    control_action VARCHAR(20) CHECK (control_action IN ('start', 'pause', 'resume', 'stop')),
    control_reason TEXT,
    controlled_by  UUID REFERENCES public.users,
    is_active      BOOLEAN DEFAULT TRUE,
    created_at     TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at     TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 阅卷控制状态索引
CREATE INDEX IF NOT EXISTS idx_grading_control_states_exam ON grading_control_states (exam_id);
CREATE INDEX IF NOT EXISTS idx_grading_control_states_grader ON grading_control_states (grader_user_id);
CREATE INDEX IF NOT EXISTS idx_grading_control_states_active ON grading_control_states (is_active);

-- 学业统计表
CREATE TABLE IF NOT EXISTS academic_statistics (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id           UUID REFERENCES exams ON DELETE CASCADE,
    student_id        UUID REFERENCES students ON DELETE CASCADE,
    subject           VARCHAR(50) NOT NULL,
    total_score       NUMERIC(5, 2),
    class_rank        INTEGER,
    grade_rank        INTEGER,
    school_rank       INTEGER,
    is_absent         BOOLEAN DEFAULT FALSE,
    absent_reason     TEXT,
    performance_trend JSONB,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学业统计索引
CREATE INDEX IF NOT EXISTS idx_academic_statistics_exam ON academic_statistics (exam_id);
CREATE INDEX IF NOT EXISTS idx_academic_statistics_student ON academic_statistics (student_id);
CREATE INDEX IF NOT EXISTS idx_academic_statistics_subject ON academic_statistics (subject);

-- 题目分析表
CREATE TABLE IF NOT EXISTS question_analysis (
    id                       UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id                  UUID REFERENCES exams ON DELETE CASCADE,
    question_id              UUID NOT NULL,
    question_type            VARCHAR(20) NOT NULL,
    total_students           INTEGER,
    correct_count            INTEGER,
    score_rate               NUMERIC(5, 2),
    average_score            NUMERIC(5, 2),
    score_distribution       JSONB,
    option_distribution      JSONB,
    zero_score_count         INTEGER,
    full_score_count         INTEGER,
    difficulty_coefficient   NUMERIC(3, 2),
    discrimination_index     NUMERIC(3, 2),
    knowledge_points_mastery JSONB,
    created_at               TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题目分析索引
CREATE INDEX IF NOT EXISTS idx_question_analysis_exam ON question_analysis (exam_id);
CREATE INDEX IF NOT EXISTS idx_question_analysis_question ON question_analysis (question_id);
CREATE INDEX IF NOT EXISTS idx_question_analysis_type ON question_analysis (question_type);

-- 题目得分表
CREATE TABLE IF NOT EXISTS question_scores (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id          UUID REFERENCES exams ON DELETE CASCADE,
    student_id       UUID REFERENCES students ON DELETE CASCADE,
    question_id      UUID NOT NULL,
    question_type    VARCHAR(20) NOT NULL,
    max_score        NUMERIC(5, 2) NOT NULL,
    actual_score     NUMERIC(5, 2) NOT NULL,
    score_percentage NUMERIC(5, 2) GENERATED ALWAYS AS ((actual_score / max_score) * 100) STORED,
    answer_content   TEXT,
    is_correct       BOOLEAN,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    knowledge_points JSONB,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题目得分索引
CREATE INDEX IF NOT EXISTS idx_question_scores_exam ON question_scores (exam_id);
CREATE INDEX IF NOT EXISTS idx_question_scores_student ON question_scores (student_id);
CREATE INDEX IF NOT EXISTS idx_question_scores_question ON question_scores (question_id);
CREATE INDEX IF NOT EXISTS idx_question_scores_correct ON question_scores (is_correct);

-- 表注释
COMMENT ON TABLE grading_statistics IS '阅卷统计表 - 统计阅卷过程的各项指标';
COMMENT ON COLUMN grading_statistics.consistency_score IS '阅卷一致性评分';
COMMENT ON COLUMN grading_statistics.score_distribution IS '分数分布（JSON格式）';

COMMENT ON TABLE grading_control_states IS '阅卷控制状态表 - 管理阅卷过程的控制状态';
COMMENT ON COLUMN grading_control_states.control_level IS '控制级别：全局、公共资源、阅卷员';

COMMENT ON TABLE academic_statistics IS '学业统计表 - 学生考试成绩统计';
COMMENT ON COLUMN academic_statistics.performance_trend IS '成绩趋势分析（JSON格式）';

COMMENT ON TABLE question_analysis IS '题目分析表 - 题目难度和区分度分析';
COMMENT ON COLUMN question_analysis.difficulty_coefficient IS '难度系数';
COMMENT ON COLUMN question_analysis.discrimination_index IS '区分度指数';

COMMENT ON TABLE question_scores IS '题目得分表 - 学生具体题目得分记录';
