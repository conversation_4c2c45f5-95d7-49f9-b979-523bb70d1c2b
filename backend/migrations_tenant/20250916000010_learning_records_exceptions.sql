-- Migration: Learning Records and Exception Handling
-- Description: Create learning records, version tracking, and exception handling tables
-- Created: 2025-01-16

-- 学习记录表
CREATE TABLE IF NOT EXISTS learning_records (
    id                    UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id            UUID NOT NULL REFERENCES students,
    question_id           UUID NOT NULL,
    grading_record_id     UUID REFERENCES card_block_grading_records,
    exam_id               UUID REFERENCES exams,
    subject               VARCHAR(50) NOT NULL,
    knowledge_points      JSONB,
    difficulty_level      INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    student_score         NUMERIC(5, 2) NOT NULL,
    max_score             NUMERIC(5, 2) NOT NULL,
    score_rate            NUMERIC(5, 2) GENERATED ALWAYS AS ((student_score / max_score) * 100) STORED,
    mastery_level         VARCHAR(20) CHECK (mastery_level IN ('excellent', 'good', 'fair', 'poor', 'not_mastered')),
    learning_suggestions  JSONB,
    recommended_exercises JSONB,
    improvement_areas     JSONB,
    historical_comparison JSONB,
    generated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at            TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (student_id, question_id, exam_id)
);

-- 学习记录索引
CREATE INDEX IF NOT EXISTS idx_learning_records_student ON learning_records (student_id);
CREATE INDEX IF NOT EXISTS idx_learning_records_question ON learning_records (question_id);
CREATE INDEX IF NOT EXISTS idx_learning_records_exam ON learning_records (exam_id);
CREATE INDEX IF NOT EXISTS idx_learning_records_subject ON learning_records (subject);
CREATE INDEX IF NOT EXISTS idx_learning_records_mastery ON learning_records (mastery_level);

-- 学习记录版本表
CREATE TABLE IF NOT EXISTS learning_record_versions (
    id                 UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    learning_record_id UUID REFERENCES learning_records ON DELETE CASCADE,
    version_number     INTEGER NOT NULL,
    change_reason      VARCHAR(100),
    changed_fields     JSONB,
    previous_data      JSONB,
    changed_by         UUID REFERENCES public.users,
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学习记录版本索引
CREATE INDEX IF NOT EXISTS idx_learning_record_versions_record ON learning_record_versions (learning_record_id);
CREATE INDEX IF NOT EXISTS idx_learning_record_versions_version ON learning_record_versions (version_number);

-- 学生ID异常表
CREATE TABLE IF NOT EXISTS student_id_exceptions (
    id                   UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id              UUID,
    detected_student_id  VARCHAR(50),
    exception_type       VARCHAR(30) CHECK (exception_type IN ('unrecognized', 'blurred', 'missing', 'invalid', 'duplicate')),
    suggested_students   JSONB,
    confirmed_student_id UUID REFERENCES students,
    confirmed_by         UUID REFERENCES public.users,
    confirmed_at         TIMESTAMP WITH TIME ZONE,
    resolution_method    VARCHAR(20) CHECK (resolution_method IN ('auto_match', 'manual_input', 'name_match', 'teacher_confirm')),
    processing_notes     TEXT,
    created_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学生ID异常索引
CREATE INDEX IF NOT EXISTS idx_student_id_exceptions_scan ON student_id_exceptions (scan_id);
CREATE INDEX IF NOT EXISTS idx_student_id_exceptions_type ON student_id_exceptions (exception_type);
CREATE INDEX IF NOT EXISTS idx_student_id_exceptions_confirmed ON student_id_exceptions (confirmed_student_id);

-- 试卷扫描异常表
CREATE TABLE IF NOT EXISTS paper_scan_exceptions (
    id                    UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id               UUID,
    exception_type        VARCHAR(30) CHECK (exception_type IN ('duplicate', 'blank', 'blurred', 'damaged', 'orientation', 'other')),
    exception_description TEXT,
    auto_detected         BOOLEAN DEFAULT TRUE,
    confidence_score      NUMERIC(3, 2),
    resolution_status     VARCHAR(20) DEFAULT 'pending' CHECK (resolution_status IN ('pending', 'resolved', 'ignored')),
    resolved_by           UUID REFERENCES public.users,
    resolved_at           TIMESTAMP WITH TIME ZONE,
    resolution_notes      TEXT,
    created_at            TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 试卷扫描异常索引
CREATE INDEX IF NOT EXISTS idx_paper_scan_exceptions_scan ON paper_scan_exceptions (scan_id);
CREATE INDEX IF NOT EXISTS idx_paper_scan_exceptions_type ON paper_scan_exceptions (exception_type);
CREATE INDEX IF NOT EXISTS idx_paper_scan_exceptions_status ON paper_scan_exceptions (resolution_status);

-- 表注释
COMMENT ON TABLE learning_records IS '学习记录表 - 记录学生的学习情况和知识掌握度';
COMMENT ON COLUMN learning_records.knowledge_points IS '知识点列表（JSON格式）';
COMMENT ON COLUMN learning_records.mastery_level IS '掌握程度等级';
COMMENT ON COLUMN learning_records.learning_suggestions IS '学习建议（JSON格式）';
COMMENT ON COLUMN learning_records.recommended_exercises IS '推荐练习（JSON格式）';

COMMENT ON TABLE learning_record_versions IS '学习记录版本表 - 跟踪学习记录的变更历史';
COMMENT ON COLUMN learning_record_versions.changed_fields IS '变更字段列表（JSON格式）';
COMMENT ON COLUMN learning_record_versions.previous_data IS '变更前的数据（JSON格式）';

COMMENT ON TABLE student_id_exceptions IS '学生ID异常表 - 处理学生身份识别异常';
COMMENT ON COLUMN student_id_exceptions.exception_type IS '异常类型：无法识别、模糊、缺失、无效、重复';
COMMENT ON COLUMN student_id_exceptions.suggested_students IS '建议的学生列表（JSON格式）';

COMMENT ON TABLE paper_scan_exceptions IS '试卷扫描异常表 - 处理试卷扫描过程中的异常';
COMMENT ON COLUMN paper_scan_exceptions.exception_type IS '异常类型：重复、空白、模糊、损坏、方向、其他';
COMMENT ON COLUMN paper_scan_exceptions.auto_detected IS '是否自动检测到的异常';
