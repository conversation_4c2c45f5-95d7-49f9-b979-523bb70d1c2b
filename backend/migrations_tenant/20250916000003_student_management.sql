-- Migration: Student Management System
-- Description: Create student information and profile management tables
-- Created: 2025-01-16

-- 学生基本信息表
CREATE TABLE IF NOT EXISTS students (
    id                      UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_number          VARCHAR(50) NOT NULL UNIQUE,
    student_name            VARCHAR(100) NOT NULL,
    gender                  VARCHAR(10),
    birth_date              DATE,
    id_number               VARCHAR(20),
    phone                   VARCHAR(20),
    email                   VARCHAR(100),
    address                 TEXT,
    guardian_name           VARCHAR(100),
    guardian_phone          VARCHAR(20),
    guardian_relation       VARCHAR(20),
    administrative_class_id UUID,
    grade_level_id          UUID REFERENCES public.grade_levels,
    user_id                 UUID REFERENCES public.users,
    enrollment_date         DATE,
    status                  VARCHAR(20) DEFAULT 'active'
        CHECK (status IN ('active', 'inactive', 'graduated', 'transferred')),
    profile_level           VARCHAR(20)
        CHECK (profile_level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')),
    profile_tags            JSONB,
    notes                   TEXT,
    created_at              TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at              TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学生基本信息索引
CREATE INDEX IF NOT EXISTS idx_students_class ON students (administrative_class_id);
CREATE INDEX IF NOT EXISTS idx_students_grade ON students (grade_level_id);
CREATE INDEX IF NOT EXISTS idx_students_user ON students (user_id);
CREATE INDEX IF NOT EXISTS idx_students_student_number ON students (student_number);
CREATE INDEX IF NOT EXISTS idx_students_status ON students (status);

-- 学生档案等级表
CREATE TABLE IF NOT EXISTS student_profile_levels (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id        UUID REFERENCES students ON DELETE CASCADE,
    subject           VARCHAR(50) NOT NULL,
    level             VARCHAR(20)
        CHECK (level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')),
    level_description TEXT,
    assessment_date   DATE NOT NULL,
    assessed_by       UUID REFERENCES public.users,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (student_id, subject, assessment_date)
);

-- 学生档案等级索引
CREATE INDEX IF NOT EXISTS idx_student_profile_levels_student ON student_profile_levels (student_id);

-- 学生档案标签表
CREATE TABLE IF NOT EXISTS student_profile_tags (
    id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id   UUID REFERENCES students ON DELETE CASCADE,
    tag_name     VARCHAR(50) NOT NULL,
    tag_value    VARCHAR(100),
    tag_category VARCHAR(30)
        CHECK (tag_category IN ('academic', 'behavior', 'interest', 'ability', 'other')),
    created_by   UUID REFERENCES public.users,
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (student_id, tag_name)
);

-- 学生档案标签索引
CREATE INDEX IF NOT EXISTS idx_student_profile_tags_student ON student_profile_tags (student_id);

-- 表注释
COMMENT ON TABLE students IS '学生基本信息表';
COMMENT ON COLUMN students.student_number IS '学生学号，唯一标识';
COMMENT ON COLUMN students.student_name IS '学生姓名';
COMMENT ON COLUMN students.administrative_class_id IS '行政班级ID';
COMMENT ON COLUMN students.profile_level IS '学生档案等级';
COMMENT ON COLUMN students.profile_tags IS '学生标签（JSON格式）';

COMMENT ON TABLE student_profile_levels IS '学生学科档案等级表';
COMMENT ON COLUMN student_profile_levels.subject IS '学科代码';
COMMENT ON COLUMN student_profile_levels.level IS '等级评定';
COMMENT ON COLUMN student_profile_levels.assessment_date IS '评定日期';

COMMENT ON TABLE student_profile_tags IS '学生档案标签表';
COMMENT ON COLUMN student_profile_tags.tag_category IS '标签分类';
