# 模块化租户 Schema 迁移指南

本指南介绍新的模块化租户 schema 迁移系统，该系统将原来的单一 `init_tenant_schema.sql` 按功能模块拆分为多个独立的迁移文件。

## 🏗️ 架构概述

### 目录结构
```
backend/
├── migrations/                    # Public schema 迁移 (标准 SQLx)
├── migrations_tenant/             # Tenant schema 迁移 (模块化)
│   ├── 20250916000001_user_identity_system.sql
│   ├── 20250916000002_subject_organization.sql
│   ├── 20250916000003_student_management.sql
│   ├── 20250916000004_teacher_management.sql
│   ├── 20250916000005_class_management.sql
│   ├── 20250916000006_exam_management.sql
│   ├── 20250916000007_answer_card_system.sql
│   ├── 20250916000008_grading_system.sql
│   ├── 20250916000009_statistics_analytics.sql
│   ├── 20250916000010_learning_records_exceptions.sql
│   ├── 20250916000011_homework_system.sql
│   └── 20250916000012_paper_scanning_system.sql
├── src/service/tenant/
│   └── sqlx_schema_migration_service.rs
└── src/bin/
    └── tenant_migrate.rs
```

## 📋 模块化迁移结构

原来的 `init_tenant_schema.sql` 已按功能模块拆分为以下迁移文件：

### 1. 用户身份系统 (20250916000001_user_identity_system.sql)
**功能**: 用户身份和权限管理
**表结构**:
- `user_identities`: 用户身份和权限管理

### 2. 学科组织结构 (20250916000002_subject_organization.sql)
**功能**: 学科组织和成员管理
**表结构**:
- `subject_groups`: 学科组管理
- `subject_group_members`: 学科组成员关系

### 3. 学生管理 (20250916000003_student_management.sql)
**功能**: 学生信息和档案管理
**表结构**:
- `students`: 学生基本信息
- `student_profile_levels`: 学生档案等级
- `student_profile_tags`: 学生档案标签

### 4. 教师管理 (20250916000004_teacher_management.sql)
**功能**: 教师信息和就职管理
**表结构**:
- `teachers`: 教师信息和就职状态

### 5. 班级管理 (20250916000005_class_management.sql)
**功能**: 行政班级和教学班级管理
**表结构**:
- `administrative_classes`: 行政班级
- `teaching_classes`: 教学班级
- `student_teaching_classes`: 学生教学班级关联

### 6. 考试管理 (20250916000006_exam_management.sql)
**功能**: 考试基本信息和联考管理
**表结构**:
- `exams`: 考试基本信息
- `exam_subjects`: 考试学科
- `exam_students`: 考试学生
- `joint_exams`: 联考管理

### 7. 答题卡系统 (20250916000007_answer_card_system.sql)
**功能**: 答题卡题块和评分标准管理
**表结构**:
- `answer_card_blocks`: 答题卡题块
- `card_block_question_links`: 题块问题关联
- `answer_block_scoring_criteria`: 评分标准
- `scores`, `score_details`, `score_blocks`: 分数管理

### 8. 阅卷系统 (20250916000008_grading_system.sql)
**功能**: 阅卷分配、记录和AI阅卷管理
**表结构**:
- `grading_assignments`: 阅卷分配
- `grading_records`: 阅卷记录
- `card_block_grading_records`: 题块阅卷记录
- `ai_grading_records`: AI阅卷记录

### 9. 统计分析 (20250916000009_statistics_analytics.sql)
**功能**: 阅卷统计、学业分析和题目分析
**表结构**:
- `grading_statistics`: 阅卷统计
- `grading_control_states`: 阅卷控制状态
- `academic_statistics`: 学业统计
- `question_analysis`: 题目分析
- `question_scores`: 题目得分

### 10. 学习记录和异常处理 (20250916000010_learning_records_exceptions.sql)
**功能**: 学习记录跟踪和异常处理
**表结构**:
- `learning_records`: 学习记录
- `learning_record_versions`: 学习记录版本
- `student_id_exceptions`: 学生ID异常
- `paper_scan_exceptions`: 试卷扫描异常

### 11. 作业系统 (20250916000011_homework_system.sql)
**功能**: 作业管理和提交跟踪
**表结构**:
- `homework`: 作业管理
- `papers`: 试卷内容
- `homework_papers`: 作业试卷关联
- `homework_subjects`: 作业学科
- `homework_students`: 作业学生
- `homework_feedback`: 作业反馈
- `homework_submission_history`: 作业提交历史

### 12. 试卷扫描系统 (20250916000012_paper_scanning_system.sql)
**功能**: 试卷扫描和处理管理
**表结构**:
- `paper_scans`: 试卷扫描
- `paper_scan_pages`: 扫描页面
- `paper_scan_blocks`: 扫描块
- `scan_batches`: 扫描批次

## 🔍 索引命名优化

### 问题解决: 多 Schema 下的索引重名
**结论**: PostgreSQL 中索引名称在 schema 级别唯一，不同 schema 可以有相同名称的索引，不会产生冲突。

**优化措施**:
1. **移除 schema 前缀**: 新的迁移文件使用简洁的索引命名
   - 旧命名: `idx_{schema}_subject_groups_subject`
   - 新命名: `idx_subject_groups_subject`

2. **标准化命名规范**: 
   - 格式: `idx_<table_name>_<column_name(s)>`
   - 示例: `idx_students_class`, `idx_grading_records_exam`

## 🚀 使用方法

### 1. 执行模块化迁移

```bash
# 迁移所有租户（按模块顺序执行）
cargo run --bin tenant-migrate migrate --all

# 迁移特定租户
cargo run --bin tenant-migrate migrate --tenant tenant_gzitv

# 查看迁移状态
cargo run --bin tenant-migrate status
```

### 2. 验证迁移结果

```bash
# 验证所有迁移文件
cargo run --bin tenant-migrate validate

# 检查特定租户的表结构
psql -d your_db -c "\dt tenant_001.*"
```

## 💡 优势

### 1. 模块化管理
- **独立维护**: 每个功能模块可以独立开发和维护
- **清晰职责**: 每个迁移文件职责明确，便于理解
- **版本控制**: 更好的版本控制和代码审查体验

### 2. 部署灵活性
- **渐进式部署**: 可以按模块逐步部署新功能
- **回滚控制**: 可以精确控制回滚范围
- **依赖管理**: 清晰的模块依赖关系

### 3. 维护便利性
- **问题定位**: 快速定位问题所在的功能模块
- **性能优化**: 针对特定模块进行性能优化
- **文档完善**: 每个模块都有详细的表和字段注释

## 🔧 迁移策略

### 从旧系统迁移
如果你的系统当前使用的是单一的 `init_tenant_schema.sql`：

1. **备份现有数据**: 确保数据安全
2. **测试环境验证**: 在测试环境中验证新的模块化迁移
3. **逐步替换**: 可以保留旧的迁移文件，新功能使用模块化迁移
4. **完全切换**: 验证无误后完全切换到新的模块化系统

### 新项目使用
对于新项目，直接使用模块化迁移系统：

1. **按需选择**: 根据业务需求选择需要的模块
2. **自定义扩展**: 基于现有模块创建自定义功能
3. **标准化开发**: 遵循模块化的开发模式

## 📚 最佳实践

1. **命名规范**: 遵循统一的表名和索引命名规范
2. **注释完善**: 为每个表和重要字段添加注释
3. **索引优化**: 根据查询模式添加合适的索引
4. **约束完整**: 添加必要的外键约束和检查约束
5. **版本管理**: 严格按照时间戳命名迁移文件

这个模块化的迁移系统为 deep-mate 项目提供了更好的可维护性和扩展性！
