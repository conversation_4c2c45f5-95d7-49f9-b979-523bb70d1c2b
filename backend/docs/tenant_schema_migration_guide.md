# 租户 Schema 迁移指南

本指南介绍如何使用基于 SQLx 的租户 schema 迁移系统来管理多租户数据库结构版本。

## 概述

我们的多租户系统采用以下架构：

- **Public Schema**: 存储全局数据（用户、角色、租户信息等）
- **Tenant Schemas**: 每个租户有独立的 schema，存储业务数据
- **双重迁移系统**: 
  - Public schema 使用标准的 `sqlx::migrate!()`
  - Tenant schemas 使用自定义的 `SqlxSchemaMigrationService`

## 迁移目录结构

```
backend/
├── migrations/                    # Public schema 迁移 (标准 SQLx)
│   ├── 20250101_*.sql
│   └── 20250102_*.sql
└── migrations_tenant/             # Tenant schema 迁移 (自定义)
    ├── 20250116000001_initial_tenant_schema.sql
    └── 20250116000002_homework_system.sql
```

## 使用方法

### 1. 查看迁移状态

```bash
# 查看所有租户的迁移状态
cargo run --bin tenant-migrate status

# 查看特定租户的迁移状态
cargo run --bin tenant-migrate status --schema tenant_001
```

### 2. 执行迁移

```bash
# 迁移所有租户 schema
cargo run --bin tenant-migrate migrate --all

# 迁移特定租户 schema
cargo run --bin tenant-migrate migrate --schema tenant_001
```

### 3. 创建新的迁移

```bash
# 创建新的租户迁移文件
cargo run --bin tenant-migrate create --description "Add notification system"
```

### 4. 验证迁移文件

```bash
# 验证所有迁移文件的语法
cargo run --bin tenant-migrate validate
```

## 编写迁移文件

### 租户迁移文件规则

1. **文件命名**: `YYYYMMDDHHMMSS_description.sql`
2. **不使用 schema 前缀**: 迁移系统会自动设置正确的 search_path
3. **支持标准 SQL**: 可以使用 CREATE, ALTER, DROP 等标准 SQL 语句

### 示例迁移文件

```sql
-- migrations_tenant/20250116000003_add_notifications.sql
-- Migration: Add notification system
-- Description: Add notification tables for tenant communication
-- Created: 2025-01-16 10:30:00 UTC

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_id UUID NOT NULL,
    recipient_type VARCHAR(20) NOT NULL CHECK (recipient_type IN ('student', 'teacher', 'parent')),
    notification_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_notifications_recipient 
    ON notifications (recipient_id, recipient_type);
CREATE INDEX IF NOT EXISTS idx_notifications_read 
    ON notifications (is_read);
```

## 程序集成

### 在应用启动时自动迁移

```rust
// 在 lib.rs 中
use service::tenant::sqlx_schema_migration_service::SqlxSchemaMigrationService;

// 运行租户 Schema 迁移
info!("🔄 Running tenant schema migrations...");
match SqlxSchemaMigrationService::new(pool.clone(), "migrations_tenant").await {
    Ok(tenant_migration_service) => {
        if let Err(e) = tenant_migration_service.migrate_all_tenants().await {
            error!("❌ Some tenant schema migrations failed: {}", e);
        } else {
            info!("✅ All tenant schema migrations completed");
        }
    }
    Err(e) => {
        error!("❌ Failed to initialize tenant migration service: {}", e);
    }
}
```

### 在代码中使用迁移服务

```rust
use crate::service::tenant::sqlx_schema_migration_service::SqlxSchemaMigrationService;

// 为新租户执行迁移
async fn setup_new_tenant(pool: &PgPool, schema_name: &str) -> Result<()> {
    let migration_service = SqlxSchemaMigrationService::new(
        pool.clone(), 
        "migrations_tenant"
    ).await?;
    
    migration_service.migrate_tenant_schema(schema_name).await?;
    Ok(())
}

// 检查租户迁移状态
async fn check_tenant_status(pool: &PgPool, schema_name: &str) -> Result<()> {
    let migration_service = SqlxSchemaMigrationService::new(
        pool.clone(), 
        "migrations_tenant"
    ).await?;
    
    let status = migration_service.get_migration_status(schema_name).await?;
    println!("Applied: {}, Pending: {}", status.applied_count, status.pending_count);
    Ok(())
}
```

## 最佳实践

### 1. 迁移文件编写

- **向前兼容**: 新迁移应该与现有数据兼容
- **幂等性**: 迁移应该可以安全地重复执行
- **测试**: 在开发环境中充分测试迁移
- **备份**: 在生产环境执行迁移前备份数据

### 2. 版本管理

- **顺序执行**: 迁移按时间戳顺序执行
- **不可修改**: 已应用的迁移文件不应修改
- **回滚计划**: 为重要迁移准备回滚方案

### 3. 性能考虑

- **索引**: 为新字段添加适当的索引
- **批量操作**: 大量数据变更使用批量操作
- **锁定时间**: 最小化表锁定时间

## 故障排除

### 常见问题

1. **迁移失败**: 检查 SQL 语法和数据约束
2. **权限问题**: 确保数据库用户有足够权限
3. **Schema 不存在**: 迁移系统会自动创建缺失的 schema

### 调试技巧

```bash
# 启用详细日志
RUST_LOG=debug cargo run --bin tenant-migrate migrate --all

# 检查特定租户状态
cargo run --bin tenant-migrate status --schema problematic_tenant
```

## 环境配置

确保在 `.env` 文件中配置正确的数据库连接：

```env
DATABASE_URL=postgresql://username:password@localhost/database_name
```

## 与标准 SQLx 迁移的区别

| 特性 | 标准 SQLx 迁移 | 租户 Schema 迁移 |
|------|----------------|------------------|
| 目标 | Public Schema | 每个租户 Schema |
| 执行方式 | `sqlx::migrate!()` | `SqlxSchemaMigrationService` |
| Search Path | 默认 | 动态设置 |
| 并发执行 | 单一 | 多租户并行 |
| 错误处理 | 中断启动 | 记录错误继续 |

这种设计既保持了 SQLx 迁移的标准化优势，又满足了多租户架构的特殊需求。
