# 租户创建流程升级：从单体脚本到模块化迁移

## 📋 概述

本文档描述了 deep-mate 项目中租户创建流程的重大升级，从使用单体 SQL 脚本转换为基于 SQLx 的模块化迁移系统。

## 🔄 变更内容

### 旧的租户创建流程

**问题**：
- 使用单体 `tenants/template/init_tenant_schema.sql` 文件
- 难以维护和版本控制
- 无法进行增量更新
- 缺乏回滚机制

**代码位置**：
```rust
// backend/src/service/tenant/tenant_service.rs (旧版本)
async fn initialize_tenant_tables(&self, conn: &mut sqlx::PgConnection, schema_name: &str) -> Result<()> {
    // 读取租户模板SQL文件
    let template_path = "tenants/template/init_tenant_schema.sql";
    let raw_sql = std::fs::read_to_string(template_path)?;
    
    // 替换模板中的schema占位符
    let replaced_sql = raw_sql.replace("{schema}", schema_name);
    
    // 执行SQL批处理
    self.execute_sql_batch(conn, &replaced_sql).await?;
    Ok(())
}
```

### 新的租户创建流程

**优势**：
- 使用 SQLx 标准迁移系统
- 12 个功能模块化的迁移文件
- 支持版本控制和增量更新
- 内置回滚和状态跟踪

**代码位置**：
```rust
// backend/src/service/tenant/tenant_service.rs (新版本)
async fn initialize_tenant_schema_with_migrations(&self, schema_name: &str) -> Result<()> {
    // 创建迁移服务实例
    let migration_service = SqlxSchemaMigrationService::new(
        self.pool.clone(), 
        "migrations_tenant"
    ).await?;
    
    // 执行租户 schema 迁移
    migration_service.migrate_tenant_schema(schema_name).await?;
    
    Ok(())
}
```

## 📁 新的迁移文件结构

```
backend/migrations_tenant/
├── 20250116000001_user_identity_system.sql      # 用户身份系统
├── 20250116000002_subject_organization.sql     # 学科组织结构
├── 20250116000003_student_management.sql       # 学生管理
├── 20250116000004_teacher_management.sql       # 教师管理
├── 20250116000005_class_management.sql         # 班级管理
├── 20250116000006_exam_management.sql          # 考试管理
├── 20250116000007_answer_card_system.sql       # 答题卡系统
├── 20250116000008_grading_system.sql           # 阅卷系统
├── 20250116000009_statistics_analytics.sql    # 统计分析
├── 20250116000010_learning_records_exceptions.sql # 学习记录和异常处理
├── 20250116000011_homework_system.sql          # 作业系统
└── 20250116000012_paper_scanning_system.sql    # 试卷扫描系统
```

## 🔧 技术实现

### 1. 租户服务更新

**文件**: `backend/src/service/tenant/tenant_service.rs`

**关键变更**:
- 添加 `SqlxSchemaMigrationService` 依赖
- 替换 `initialize_tenant_tables` 为 `initialize_tenant_schema_with_migrations`
- 调整事务处理逻辑

### 2. 迁移服务集成

**文件**: `backend/src/service/tenant/sqlx_schema_migration_service.rs`

**功能**:
- 基于 SQLx `Migrator` 的租户迁移
- 支持多租户并行迁移
- 完整的错误处理和状态跟踪

### 3. 索引命名优化

**变更**:
- 移除 schema 前缀：`idx_{schema}_table_field` → `idx_table_field`
- PostgreSQL 索引名称在 schema 级别唯一，无需全局唯一性

## ✅ 验证和测试

### 1. 索引重名测试

**文件**: `backend/src/bin/test_multi_schema_indexes.rs`

**验证内容**:
- 不同 schema 可以有相同名称的索引
- 同一 schema 内索引名称必须唯一
- 索引功能正常工作

### 2. 编译验证

```bash
cd backend && cargo check
```

## 🚀 使用方法

### 创建新租户

现在创建租户时会自动使用新的模块化迁移系统：

```rust
// 通过 API 创建租户
POST /api/tenants
{
    "name": "新租户",
    "schema_name": "tenant_new",
    "tenant_type": "school",
    "domain": "new.example.com"
}
```

### 手动执行租户迁移

```bash
# 为所有租户执行迁移
cargo run --bin tenant-migrate migrate --all

# 为特定租户执行迁移
cargo run --bin tenant-migrate migrate --schema tenant_example

# 查看迁移状态
cargo run --bin tenant-migrate status
```

## 📊 优势对比

| 特性 | 旧系统 | 新系统 |
|------|--------|--------|
| 维护性 | ❌ 单体文件难维护 | ✅ 模块化易维护 |
| 版本控制 | ❌ 无版本跟踪 | ✅ 完整版本管理 |
| 增量更新 | ❌ 只能全量替换 | ✅ 支持增量迁移 |
| 回滚能力 | ❌ 无回滚机制 | ✅ 内置回滚支持 |
| 错误处理 | ❌ 基础错误处理 | ✅ 完善错误隔离 |
| 并发安全 | ❌ 无并发控制 | ✅ 事务级并发控制 |
| 状态跟踪 | ❌ 无状态记录 | ✅ 完整状态跟踪 |

## 🎯 结论

通过这次升级，租户创建流程变得更加：

1. **可维护** - 模块化结构便于开发和维护
2. **可靠** - 基于 SQLx 的成熟迁移机制
3. **灵活** - 支持增量更新和精确回滚
4. **安全** - 完善的错误处理和事务控制

这为 deep-mate 项目的长期发展奠定了坚实的基础。
