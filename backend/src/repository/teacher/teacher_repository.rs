use sqlx::{PgPool, Transaction, Postgres};
use uuid::Uuid;
use anyhow::Result;

/// 教师数据访问层
#[derive(Clone)]
pub struct TeacherRepository {
    db_pool: PgPool,
}

impl TeacherRepository {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 根据用户ID获取绑定的教师ID
    pub async fn find_teacher_id_by_user_id(
        &self,
        schema_name: &str,
        user_id: &Uuid,
    ) -> Result<Option<Uuid>, sqlx::Error> {
        let query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            schema_name
        );
        
        sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_optional(&self.db_pool)
            .await
    }

    /// 根据教师ID获取用户ID
    pub async fn get_user_id_by_teacher_id(
        &self,
        schema_name: &str,
        teacher_id: &Uuid,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<Option<Uuid>> {
        let query = format!(
            "SELECT user_id FROM {}.teachers WHERE id = $1",
            schema_name
        );

        let user_id = sqlx::query_scalar(&query)
            .bind(teacher_id)
            .fetch_optional(&mut **tx)
            .await?;

        Ok(user_id)
    }
}
