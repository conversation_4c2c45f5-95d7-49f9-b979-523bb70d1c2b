use sqlx::{PgPool, Transaction, Postgres};
use uuid::Uuid;
use anyhow::Result;
use crate::model::{
    administrative_classes::administrative_classes::{AdministrativeClasses, CreateAdministrativeClassesParams, PageUserClassListParams},
};

#[derive(Clone)]
pub struct AdministrativeClassesRepository {
    db_pool: PgPool,
}

impl AdministrativeClassesRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /// 获取所有行政班列表（管理员功能）
    pub async fn get_all_class_list(
        &self,
        schema_name: &str,
    ) -> Result<Vec<AdministrativeClasses>> {
        let query = format!(
            "SELECT * from {}.administrative_classes ac order by ac.is_active desc , ac.created_at desc",
            schema_name
        );

        let classes = sqlx::query_as::<_, AdministrativeClasses>(&query)
            .fetch_all(&self.db_pool)
            .await?;

        Ok(classes)
    }

    /// 分页查询班级列表（管理员功能）
    pub async fn page_all_class_list(
        &self,
        schema_name: &str,
        params: &PageUserClassListParams,
    ) -> Result<(Vec<AdministrativeClasses>, i64)> {
        let PageUserClassListParams { page_params, name_like, class_code, is_active } = params;

        let mut query_builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where 1=1 ",
            schema_name
        ));
        let mut count_builder = sqlx::QueryBuilder::new(format!(
            "SELECT count(*) from {}.administrative_classes ac where 1=1 ",
            schema_name
        ));

        // Add conditions
        if let Some(name_like) = name_like {
            query_builder.push(" and ac.class_name like ");
            query_builder.push_bind(format!("%{}%", name_like));
            count_builder.push(" and ac.class_name like ");
            count_builder.push_bind(format!("%{}%", name_like));
        }
        if let Some(class_code) = class_code {
            query_builder.push(" and ac.code = ");
            query_builder.push_bind(class_code);
            count_builder.push(" and ac.code = ");
            count_builder.push_bind(class_code);
        }
        if let Some(is_active) = is_active {
            query_builder.push(" and ac.is_active = ");
            query_builder.push_bind(is_active);
            count_builder.push(" and ac.is_active = ");
            count_builder.push_bind(is_active);
        }

        query_builder.push(" order by ac.is_active desc , ac.created_at desc ");
        query_builder.push(" limit ");
        query_builder.push_bind(page_params.get_limit());
        query_builder.push(" offset ");
        query_builder.push_bind(page_params.get_offset());

        let classes = query_builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await?;

        let total = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;

        Ok((classes, total))
    }

    /// 查询担任班主任的班级列表
    pub async fn find_head_teacher_class_list(
        &self,
        schema_name: &str,
        teacher_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>> {
        let query = format!(
            "SELECT * from {}.administrative_classes ac where ac.teacher_id = $1 order by ac.is_active desc , ac.created_at desc",
            schema_name
        );

        let classes = sqlx::query_as::<_, AdministrativeClasses>(&query)
            .bind(teacher_id)
            .fetch_all(&self.db_pool)
            .await?;

        Ok(classes)
    }

    /// 查询指定编码的班级
    pub async fn find_all_by_code(
        &self,
        schema_name: &str,
        code: &str,
    ) -> Result<Vec<AdministrativeClasses>> {
        let query = format!(
            "SELECT * from {}.administrative_classes ac where ac.code = $1",
            schema_name
        );

        let classes = sqlx::query_as::<_, AdministrativeClasses>(&query)
            .bind(code)
            .fetch_all(&self.db_pool)
            .await?;

        Ok(classes)
    }

    /// 查询指定编码的活跃班级
    pub async fn find_active_class_by_code(
        &self,
        schema_name: &str,
        code: &str,
    ) -> Result<Option<AdministrativeClasses>> {
        let query = format!(
            "SELECT * from {}.administrative_classes ac where ac.is_active = true and ac.code = $1",
            schema_name
        );

        let class = sqlx::query_as::<_, AdministrativeClasses>(&query)
            .bind(code)
            .fetch_optional(&self.db_pool)
            .await?;

        Ok(class)
    }

    /// 只更新行政班级的 grade_level_code 字段
    ///
    /// # Arguments
    /// * `schema_name` - 租户模式名称
    /// * `class_id` - 班级ID
    /// * `grade_level_code` - 新的年级代码
    ///
    /// # Returns
    /// * `Result<()>` - 成功返回 Ok(()), 失败返回错误信息
    pub async fn update_grade_level_code(
        &self,
        schema_name: &str,
        class_id: Uuid,
        grade_level_code: &str,
    ) -> Result<()> {
        let update_query = format!(
            "UPDATE {}.administrative_classes SET grade_level_code = $1, updated_at = now() WHERE id = $2",
            schema_name
        );

        sqlx::query(&update_query)
            .bind(grade_level_code)
            .bind(class_id)
            .execute(&self.db_pool)
            .await?;

        Ok(())
    }

    /// 创建行政班（仅处理 administrative_classes 表的插入）
    pub async fn create_administrative_class(
        &self,
        schema_name: &str,
        params: &CreateAdministrativeClassesParams,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<AdministrativeClasses> {
        let CreateAdministrativeClassesParams {
            class_name,
            code,
            academic_year,
            grade_level_code,
            teacher_id,
        } = params;

        let query = format!(
            "INSERT INTO {}.administrative_classes (class_name,code,academic_year,grade_level_code,teacher_id) VALUES ($1, $2, $3, $4, $5) returning *",
            schema_name
        );

        let class = sqlx::query_as::<_, AdministrativeClasses>(&query)
            .bind(class_name)
            .bind(code)
            .bind(academic_year)
            .bind(grade_level_code)
            .bind(teacher_id)
            .fetch_one(&mut **tx)
            .await?;

        Ok(class)
    }

    /// 更新行政班（仅处理 administrative_classes 表的更新）
    pub async fn update_administrative_class(
        &self,
        schema_name: &str,
        id: &Uuid,
        class_name: &str,
        code: &Option<String>,
        academic_year: &Option<String>,
        grade_level_code: &Option<String>,
        teacher_id: &Option<Uuid>,
        is_active: &bool,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<()> {
        let query = format!(
            "UPDATE {}.administrative_classes SET class_name = $1, code = $2, academic_year = $3, grade_level_code = $4, teacher_id = $5, is_active = $6, updated_at = now() WHERE id = $7",
            schema_name
        );

        sqlx::query(&query)
            .bind(class_name)
            .bind(code)
            .bind(academic_year)
            .bind(grade_level_code)
            .bind(teacher_id)
            .bind(is_active)
            .bind(id)
            .execute(&mut **tx)
            .await?;

        Ok(())
    }

    /// 删除班级（软删除）
    pub async fn delete_class(
        &self,
        schema_name: &str,
        class_id: &Uuid,
    ) -> Result<()> {
        let query = format!(
            "UPDATE {}.administrative_classes SET is_active = false WHERE id = $1",
            schema_name
        );

        sqlx::query(&query)
            .bind(class_id)
            .execute(&self.db_pool)
            .await?;

        Ok(())
    }
}
