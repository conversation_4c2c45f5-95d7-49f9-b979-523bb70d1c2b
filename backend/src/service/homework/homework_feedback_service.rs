use sqlx::PgPool;
use uuid::Uuid;
use crate::middleware::auth_middleware::AuthContext;
use crate::model::homework::homework_feedback::{HomeworkFeedback, HomeworkFeedbackStatus};
use crate::model::PageParams;
use crate::repository::homework::homework_feedback_repository::HomeworkFeedbackRepository;
use crate::repository::students::student_repository::StudentsRepository;

pub struct HomeworkFeedbackService {
    db: PgPool,
    tenant_name: String,
}

impl HomeworkFeedbackService {
    pub fn new(db: PgPool, tenant_name: String) -> Self {
        Self { db, tenant_name }
    }
    pub async fn create_feedback(&self, context: AuthContext, homework_id: Uuid, score_id: Option<Uuid>, text: String,) -> anyhow::Result<HomeworkFeedback> {
        let student_id = StudentsRepository::get_user_student_id(&self.db, &context.user_id, self.tenant_name.as_str()).await?;
        if let Some(student_id) = student_id {
            HomeworkFeedbackRepository::create_feedback(&self.db, self.tenant_name.as_str(), homework_id, student_id, score_id, text).await
        } else {
            Err(anyhow::anyhow!("用户不是学生"))
        }
    }
    pub async fn page_feedbacks(&self, homework_ids: Vec<Uuid>, page_params: PageParams, status_list: Vec<HomeworkFeedbackStatus>) -> anyhow::Result<(Vec<HomeworkFeedback>, i64)> {
        HomeworkFeedbackRepository::fetch_page_feedbacks(&self.db, self.tenant_name.as_str(), homework_ids, status_list, &page_params).await
    }
    pub async fn student_feedbacks(&self, context: AuthContext, homework_id: Uuid) -> anyhow::Result<Vec<HomeworkFeedback>> {
        let student_id = StudentsRepository::get_user_student_id(&self.db, &context.user_id, self.tenant_name.as_str()).await?;
        if let Some(student_id) = student_id {
            HomeworkFeedbackRepository::fetch_feedbacks(&self.db, self.tenant_name.as_str(), homework_id, student_id).await
        } else { Err(anyhow::anyhow!("用户不是学生")) }
    }
    pub async fn update_feedback(&self, feedback_id: Uuid, text: Option<String>, status: HomeworkFeedbackStatus) -> anyhow::Result<()> {
        HomeworkFeedbackRepository::update_feedback(&self.db, self.tenant_name.as_str(), feedback_id, status, text).await
    }
}
