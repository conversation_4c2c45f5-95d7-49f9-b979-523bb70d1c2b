#[cfg(test)]
mod tests {
    use sqlx::PgPool;
    use crate::tests::test_utils::setup_test_db;

    #[tokio::test]
    async fn test_multi_schema_index_naming() {
        let pool = setup_test_db().await;
        
        // 创建两个测试 schema
        let schema1 = "test_schema_1";
        let schema2 = "test_schema_2";
        
        // 创建 schemas
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS {}", schema1))
            .execute(&pool)
            .await
            .unwrap();
            
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS {}", schema2))
            .execute(&pool)
            .await
            .unwrap();
        
        // 在两个 schema 中创建相同的表和索引
        for schema in [schema1, schema2] {
            // 创建表
            sqlx::query(&format!(
                r#"
                CREATE TABLE {}.test_users (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    email VARCHAR(255) NOT NULL,
                    name VARCHAR(100) NOT NULL
                )
                "#,
                schema
            ))
            .execute(&pool)
            .await
            .unwrap();
            
            // 创建相同名称的索引
            sqlx::query(&format!(
                "CREATE INDEX idx_users_email ON {}.test_users (email)",
                schema
            ))
            .execute(&pool)
            .await
            .unwrap();
            
            sqlx::query(&format!(
                "CREATE INDEX idx_users_name ON {}.test_users (name)",
                schema
            ))
            .execute(&pool)
            .await
            .unwrap();
        }
        
        // 验证索引都存在且可以正常使用
        for schema in [schema1, schema2] {
            // 插入测试数据
            sqlx::query(&format!(
                "INSERT INTO {}.test_users (email, name) VALUES ($1, $2)",
                schema
            ))
            .bind(format!("test@{}.com", schema))
            .bind(format!("Test User {}", schema))
            .execute(&pool)
            .await
            .unwrap();
            
            // 使用索引查询
            let count: i64 = sqlx::query_scalar(&format!(
                "SELECT COUNT(*) FROM {}.test_users WHERE email = $1",
                schema
            ))
            .bind(format!("test@{}.com", schema))
            .fetch_one(&pool)
            .await
            .unwrap();
            
            assert_eq!(count, 1);
        }
        
        // 验证索引信息
        let indexes = sqlx::query!(
            r#"
            SELECT schemaname, indexname, tablename 
            FROM pg_indexes 
            WHERE indexname IN ('idx_users_email', 'idx_users_name')
            AND schemaname IN ($1, $2)
            ORDER BY schemaname, indexname
            "#,
            schema1,
            schema2
        )
        .fetch_all(&pool)
        .await
        .unwrap();
        
        // 应该有 4 个索引：每个 schema 2 个
        assert_eq!(indexes.len(), 4);
        
        // 验证索引分布
        let schema1_indexes = indexes.iter().filter(|i| i.schemaname == schema1).count();
        let schema2_indexes = indexes.iter().filter(|i| i.schemaname == schema2).count();
        
        assert_eq!(schema1_indexes, 2);
        assert_eq!(schema2_indexes, 2);
        
        println!("✅ Multi-schema index naming test passed!");
        println!("Found indexes:");
        for idx in indexes {
            println!("  - {}.{} on {}", idx.schemaname, idx.indexname, idx.tablename);
        }
        
        // 清理
        sqlx::query(&format!("DROP SCHEMA {} CASCADE", schema1))
            .execute(&pool)
            .await
            .unwrap();
            
        sqlx::query(&format!("DROP SCHEMA {} CASCADE", schema2))
            .execute(&pool)
            .await
            .unwrap();
    }

    #[tokio::test]
    async fn test_index_uniqueness_within_schema() {
        let pool = setup_test_db().await;
        let schema = "test_uniqueness_schema";
        
        // 创建 schema
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS {}", schema))
            .execute(&pool)
            .await
            .unwrap();
        
        // 创建两个表
        sqlx::query(&format!(
            r#"
            CREATE TABLE {}.table1 (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                data VARCHAR(255)
            )
            "#,
            schema
        ))
        .execute(&pool)
        .await
        .unwrap();
        
        sqlx::query(&format!(
            r#"
            CREATE TABLE {}.table2 (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                info VARCHAR(255)
            )
            "#,
            schema
        ))
        .execute(&pool)
        .await
        .unwrap();
        
        // 在第一个表上创建索引
        sqlx::query(&format!(
            "CREATE INDEX idx_common_name ON {}.table1 (data)",
            schema
        ))
        .execute(&pool)
        .await
        .unwrap();
        
        // 尝试在第二个表上创建相同名称的索引（应该失败）
        let result = sqlx::query(&format!(
            "CREATE INDEX idx_common_name ON {}.table2 (info)",
            schema
        ))
        .execute(&pool)
        .await;
        
        // 验证索引名称冲突
        assert!(result.is_err());
        println!("✅ Index name uniqueness within schema enforced correctly");
        
        // 清理
        sqlx::query(&format!("DROP SCHEMA {} CASCADE", schema))
            .execute(&pool)
            .await
            .unwrap();
    }
}
