use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 试卷扫描块信息
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct PaperScanBlock {
    pub id: Uuid,
    pub serial_number: i32,
    pub paper_scan_page_id: Uuid,
    pub answer_block_group_id: Uuid,
    pub answer_block_url: Option<String>,
    pub answer_content: Option<String>,
    pub status: Option<String>,
    pub abnormal_reason: Option<String>,
}

impl PaperScanBlock {
    pub fn new(id:Uuid,
               paper_scan_page_id:Uuid,
               answer_block_group_id:Uuid,
               answer_block_url:Option<String>,
               answer_content:Option<String>,
               serial_number: i32,
    )->Self{
        Self {
            id,
            serial_number,
            paper_scan_page_id,
            answer_block_group_id,
            answer_block_url,
            answer_content,
            status: None,
            abnormal_reason: None,
        }
    }
}
