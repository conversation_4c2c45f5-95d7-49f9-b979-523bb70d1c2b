import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Breadcrumb, BreadcrumbItem, Bread<PERSON>rumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { User, X, MessageSquareWarning } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import { scanApi, BatchDetail, ScoreOrBlock } from '@/services/scanApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import MathRenderer from '@/components/math/MathRenderer';
import { toast } from 'sonner';
import { studentScoreApi, FeedbackResponse, HomeworkFeedbackStatus, getStudentHomeworkDetails } from '@/services/studentScoreApi';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

type SelectQuestionType = { type: 'all' } | { type: 'score'; data: ScoreOrBlock } | null;

const StudentReport: React.FC = () => {
  const [user, setUser] = useState<BatchDetail>({} as BatchDetail);
  const [showModal, setShowModal] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [selectQuestion, setSelectQuestion] = useState<SelectQuestionType>(null);
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantName = identityInfo?.schema_name || '';
  const [feedback, setFeedback] = useState<FeedbackResponse[]>([]);

  useEffect(() => {
    fetchDetail();
    getFeedback();
  }, []);

  const handleFeedbackSubmit = () => {
    if (!feedbackText.trim()) {
      toast.error('请输入反馈内容');
      return;
    }
    let score_id: string | undefined;

    if (selectQuestion?.type === 'all') {
      score_id = undefined;
    } else if (selectQuestion?.type === 'score') {
      score_id = selectQuestion.data.Score.score.id;
    }
    let params = {
      homework_id: location.state.homework?.id,
      score_id,
      text: feedbackText,
    };
    studentScoreApi
      .submitFeedback(tenantName, params)
      .then((res) => {
        if (res.success) {
          toast.success('反馈提交成功');
          setShowModal(false);
          setSelectQuestion(null);
          setFeedbackText('');
          getFeedback();
        }
      })
      .catch((error) => {
        console.error('Error submitting feedback:', error);
        toast.error('反馈提交失败');
      });
  };

  const fetchDetail = async () => {
    if (!location.state.homework?.id) return;
    try {
      setLoading(true);
      let params = { homework_id: location.state.homework?.id };
      const response = await studentScoreApi.getStudentHomeworkDetails(tenantName, params);
      if (response.success && response.data) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFeedback = async () => {
    let params = {
      homework_id: location.state.homework?.id,
    };
    studentScoreApi
      .getFeedback(tenantName, params)
      .then((res) => {
        if (res.success) {
          setFeedback(res.data || []);
        }
      })
      .catch((error) => {
        console.error('Error getting feedback:', error);
        toast.error('获取反馈失败');
      });
  };

  const updateFeedback = async (id: string, text: string, status: HomeworkFeedbackStatus = 'Resubmitted') => {
    let params = {
      id,
      status,
      text,
    };
    studentScoreApi
      .updateFeedback(tenantName, params)
      .then((res) => {
        if (res.success) {
          toast.success('反馈更新成功');
          getFeedback();
        }
      })
      .catch((error) => {
        console.error('Error updating feedback:', error);
        toast.error('反馈更新失败');
      });
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectQuestion(null);
    setFeedbackText('');
  };

  // 监听 selectQuestion 的变化
  useEffect(() => {
    if (selectQuestion) {
      setShowModal(true);
    }
  }, [selectQuestion]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/student-score">作业列表</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>学生报告</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Card className="mt-2 flex justify-between p-5">
        <CardHeader className="p-0 flex-1">
          <CardTitle className="flex items-center gap-5">
            <span>{location.state.homework?.homework_name}</span>
            <Button
              size="sm"
              variant="outline"
              className="h-7 px-3 text-orange-600 border-orange-300 hover:bg-[#e6a23c] hover:text-white"
              onClick={() => {
                setSelectQuestion({ type: 'all' });
              }}
            >
              整卷反馈
            </Button>
            {feedback.filter((i) => i.score_id === null).length > 0 && (
              <Popover>
                <PopoverTrigger asChild>
                  <MessageSquareWarning className="w-5 h-5 text-[#e6a23c]" />
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  {feedback
                    .filter((i) => i.score_id === null)
                    .map((item, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <div className="text-sm font-bold">{item.text}</div>
                        {/* <Button variant="link" size="sm" className="text-blue-500">
                        修改
                      </Button> */}
                      </div>
                    ))}
                </PopoverContent>
              </Popover>
            )}
          </CardTitle>
          <div className="text-sm text-muted-foreground flex items-center gap-3">
            <User className="w-[18px]" />
            <span>{user.student?.student_name}</span>
            <span>{user.student?.student_number}</span>
          </div>
        </CardHeader>
        <div className="flex justify-around items-center w-[250px]">
          <div>
            <div className="text-2xl font-bold text-blue-600">{user.score_or_blocks?.reduce((sum, item) => sum + item.Score.score.score, 0)}</div>
            <div className="text-sm text-muted-foreground">得分</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{user.score_or_blocks?.reduce((sum, item) => sum + item.Score.criteria.score, 0)}</div>
            <div className="text-sm text-muted-foreground">总分</div>
          </div>
        </div>
      </Card>
      <div className="grid grid-cols-2 gap-2 mt-2 h-[calc(100vh-245px)]">
        <Card className="h-full overflow-auto">
          <CardContent className="p-0">
            {user.pages?.map((file, index) => (
              <img key={index} src={file.rectify_url} alt="" />
            ))}
          </CardContent>
        </Card>
        <Card className="p-3 h-full overflow-auto">
          <CardContent className="p-0">
            {user.score_or_blocks?.filter((i) => i.Score.criteria.scoring_type === 'Match').length > 0 && (
              <div className="flex items-center gap-3">
                <div className="text-lg font-bold">客观题</div>
                <div className="text-sm text-muted-foreground flex items-center gap-3">
                  <div>得分 {user.score_or_blocks?.filter((i) => i.Score.criteria.scoring_type === 'Match').reduce((sum, item) => sum + item.Score.score.score, 0)} 分</div>
                  <span>满分 {user.score_or_blocks?.filter((i) => i.Score.criteria.scoring_type === 'Match').reduce((sum, item) => sum + item.Score.criteria.score, 0)} 分</span>
                </div>
              </div>
            )}
            <div className="grid grid-cols-2 gap-3 mt-2">
              {user.score_or_blocks?.map(
                (item, index) =>
                  item.Score.criteria.scoring_type === 'Match' && (
                    <Card key={index} className="bg-white border border-blue-100 shadow-sm hover:shadow-md transition-shadow duration-200 group">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between gap-3 mb-3">
                          <div className="flex items-center gap-2">
                            <div className="text-lg font-bold text-blue-800">{item.Score.criteria.criteriaName}</div>
                            <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 px-2 py-0.5 text-xs">
                              客观题
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-7 px-3 text-orange-600 border-orange-300 hover:bg-[#e6a23c] hover:text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                              onClick={() => {
                                setSelectQuestion({ type: 'score', data: item });
                              }}
                            >
                              反馈异常
                            </Button>
                            {feedback.filter((i) => i.score_id === item.Score.score.id).length > 0 && (
                              <Popover>
                                <PopoverTrigger asChild>
                                  <MessageSquareWarning className="w-5 h-5 text-[#e6a23c]" />
                                </PopoverTrigger>
                                <PopoverContent className="w-80">
                                  {feedback
                                    .filter((i) => i.score_id === item.Score.score.id)
                                    .map((item, index) => (
                                      <div key={index} className="flex justify-between items-center">
                                        <div className="text-sm font-bold">{item.text}</div>
                                        {/* <Button variant="link" size="sm" className="text-blue-500">
                                          修改
                                        </Button> */}
                                      </div>
                                    ))}
                                </PopoverContent>
                              </Popover>
                            )}
                          </div>
                        </div>

                        <div className="space-y-1.5">
                          <div className="grid grid-cols-2">
                            <div className="flex text-sm gap-2">
                              <span className="text-muted-foreground">得分</span>
                              <span className="font-medium text-blue-600">{item.Score.score.score} 分</span>
                            </div>
                            <div className="flex text-sm gap-2">
                              <span className="text-muted-foreground">总分</span>
                              <span className="font-medium">{item.Score.criteria.score} 分</span>
                            </div>
                          </div>
                          <div className="grid grid-cols-2">
                            <div className="flex text-sm gap-2">
                              <span className="text-muted-foreground">作答</span>
                              <span className="font-mono text-gray-800">{item.Score.score.answer}</span>
                            </div>
                            <div className="flex text-sm gap-2">
                              <span className="text-muted-foreground">标准答案</span>
                              <span className="font-mono text-gray-800">{item.Score.criteria.answer}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ),
              )}
            </div>
            <div className="flex items-center gap-3 mt-3">
              <div className="text-lg font-bold">主观题</div>
              <div className="text-sm text-muted-foreground flex items-center gap-3">
                <div>得分 {user.score_or_blocks?.filter((i) => i.Score.criteria.scoring_type !== 'Match').reduce((sum, item) => sum + item.Score.score.score, 0)} 分</div>
                <span>满分 {user.score_or_blocks?.filter((i) => i.Score.criteria.scoring_type !== 'Match').reduce((sum, item) => sum + item.Score.criteria.score, 0)} 分</span>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-3 mt-2">
              {user.score_or_blocks
                ?.filter((i) => i.Score.criteria.scoring_type !== 'Match')
                .map((item, index) => (
                  <Card key={index} className="bg-white border border-green-100 shadow-sm hover:shadow-md transition-shadow duration-200 group">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between gap-3 mb-3">
                        <div className="flex items-center gap-2">
                          <div className="text-lg font-bold text-green-800">{item.Score.criteria.criteriaName}</div>
                          <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 px-2 py-0.5 text-xs flex items-center">
                            主观题
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-7 px-3 text-orange-600 border-orange-300 hover:bg-[#e6a23c] hover:text-white opacity-0 group-hover:opacity-100 transition-opacity duration-100"
                            onClick={() => {
                              setSelectQuestion({ type: 'score', data: item });
                            }}
                          >
                            反馈异常
                          </Button>
                          {feedback.filter((i) => i.score_id === item.Score.score.id).length > 0 && (
                            <Popover>
                              <PopoverTrigger asChild>
                                <MessageSquareWarning className="w-5 h-5 text-[#e6a23c]" />
                              </PopoverTrigger>
                              <PopoverContent className="w-80">
                                {feedback
                                  .filter((i) => i.score_id === item.Score.score.id)
                                  .map((item, index) => (
                                    <div key={index} className="flex justify-between items-center">
                                      <div className="text-sm font-bold">{item.text}</div>
                                      {/* <Button variant="link" size="sm" className="text-blue-500">
                                        修改
                                      </Button> */}
                                    </div>
                                  ))}
                              </PopoverContent>
                            </Popover>
                          )}
                        </div>
                      </div>
                      <div className="space-y-1.5">
                        <div className="flex gap-8 text-sm">
                          <div className="flex gap-2">
                            <span className="text-muted-foreground">得分</span>
                            <span className="font-medium text-green-600">{item.Score.score.score} 分</span>
                          </div>
                          <div className="flex gap-2">
                            <span className="text-muted-foreground">总分</span>
                            <span className="font-medium">{item.Score.criteria.score} 分</span>
                          </div>
                        </div>
                        <div className="flex gap-2 text-sm">
                          <span className="text-muted-foreground flex-shrink-0">作答</span>
                          <span className="font-mono text-gray-800">
                            <MathRenderer content={item.Score.score.answer} />
                          </span>
                        </div>
                        <div className="flex gap-2 text-sm">
                          <span className="text-muted-foreground flex-shrink-0">评分标准</span>
                          <span className="font-mono text-gray-800">
                            <MathRenderer content={item.Score.criteria.answer} />
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md bg-white border border-blue-100 shadow-lg">
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">反馈异常</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hover:text-blue-800"
                  onClick={() => {
                    closeModal();
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  请描述{selectQuestion?.type === 'score' ? selectQuestion?.data.Score?.criteria.criteriaName : ''}异常情况
                </label>
                <Input value={feedbackText} onChange={(e) => setFeedbackText(e.target.value)} placeholder="请输入具体问题（必填）" className="w-full" />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  className="border-blue-300 text-blue-600 hover:bg-blue-50"
                  onClick={() => {
                    closeModal();
                  }}
                >
                  取消
                </Button>
                <Button className="bg-blue-600 text-white hover:bg-blue-700" onClick={handleFeedbackSubmit}>
                  提交反馈
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default StudentReport;
