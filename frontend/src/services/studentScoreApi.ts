import { ApiResponse, PaginatedApiResponse } from '@/types';
import apiClient from './apiClient';
import { type Scores } from './homeworkReviewApi';

export interface HomeworkList {
  page_params: PageParams;
  name?: string;
  status?: string;
  subject_group_id?: string;
}

export interface PageParams {
  page: number;
  page_size: number;
}

export interface HomeworkListResponse {
  id: string;
  homework_name: string;
  homework_status: string;
  subject_group_id: string;
  description: string;
  leaf_count: number;
  leaf_total: number;
  page_count: number;
  page_total: number;
  created_at: Date;
  updated_at: Date;
}

export interface FeedbackResponse {
  id: string;
  homework_id: string;
  student_id: string;
  score_id: string;
  text: string;
  status: string;
}

export type HomeworkFeedbackStatus = 'Initial' | 'Received' | 'Rejected' | 'Cancelled' | 'Resubmitted';

export interface FeedbackUpdateParams {
  id: string;
  status: HomeworkFeedbackStatus;
  text: string;
}

export interface BatchDetail {
  id: string;
  exam_type: string;
  batch_no: string;
  student_id: string;
  student_number: string;
  created_at: Date;
  pages: PageImage[];
  score_or_blocks: ScoreOrBlock[];
  student: Student | null;
}

export interface PageImage {
  file_url: string;
  rectify_url: string;
  is_blank: boolean;
  is_abnormal: boolean;
  abnormal_reason: string | undefined;
  page_id: string;
  page_num: number;
}

export interface ScoreOrBlock {
  Block: any[];
  Score: ScoreDetail;
}

export interface ScoreDetail {
  criteria: Criteria;
  score: Scores;
}

export interface Student {
  student_id: string | null;
  student_name: string | null;
  class_name?: string | null;
  student_number: string | null;
}

export interface Criteria {
  id: string;
  answer: string;
  check_work_id: string | null;
  criteriaName: string | null;
  mode: string;
  ocr_work_id: string | null;
  score: number;
  scoring_type: 'Match' | 'Ai' | 'Tracer' | 'Man' | 'Audit';
}

export const studentScoreApi = {
  // 获取学生端作业列表
  getStudentHomeworks: async (tenant_name: string, params: HomeworkList): Promise<PaginatedApiResponse<HomeworkListResponse>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/pageStudentHomeworks`, params);
  },

  /**
   * 获取学生作业详情
   */
  getStudentHomeworkDetails: async (
    tenant_name: string,
    params:{
      homework_id: string;
    }
  ): Promise<ApiResponse<BatchDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/student_homework_detail`, params);
  },

  // 提交反馈
  submitFeedback: async (
    tenant_name: string,
    params: {
      homework_id: string;
      score_id?: string;
      text: string;
    },
  ): Promise<ApiResponse<FeedbackResponse>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/feedback/create`, params);
  },

  // 提交反馈
  updateFeedback: async (
    tenant_name: string,
    params: {
      id: string;
      status: HomeworkFeedbackStatus;
      text: string;
    },
  ): Promise<ApiResponse<FeedbackResponse>> => {
    return apiClient.put(`/api/v1/tenants/${tenant_name}/homework/feedback/update`, params);
  },

  // 获取反馈
  getFeedback: async (
    tenant_name: string,
    params: {
      homework_id: string;
    },
  ): Promise<ApiResponse<FeedbackResponse[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/feedback/student_all`, params);
  },
};
export default studentScoreApi;
