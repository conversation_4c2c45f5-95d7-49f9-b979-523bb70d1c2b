import { PropsWithChildren, useContext } from 'react';
import { useStore } from 'zustand';
import { ToolbarDataStoreContext } from '..';
import { PageSizeList } from '../config/page-size';
import { PaperDataStoreContext } from '../store/paperDataStore';
import '../ui/global.scss';
import { QuestionCardUI } from '../ui/question-card-components/global';
import './toolbar.css';

/**
 * 作者：张瀚
 * 说明：左侧工具栏
 */
export function ToolBar() {
  //取出工具栏数据
  const usePaperDataStoreContext = useContext(PaperDataStoreContext);
  const answerCard = useStore(usePaperDataStoreContext, (state) => state.answerCard);
  const getPaperData = useStore(usePaperDataStoreContext, (state) => state.getPaperData);
  const setAnswerCard = useStore(usePaperDataStoreContext, (state) => state.setAnswerCard);
  const isReadyToPrint = useStore(usePaperDataStoreContext, (state) => state.isReadyToPrint);
  const questionItemIdList = useStore(usePaperDataStoreContext, (state) => state.questionItemIdList);
  const addQuestion = useStore(usePaperDataStoreContext, (state) => state.addQuestion);
  const questionItemIdToBlockGroupIdListMap = useStore(usePaperDataStoreContext, (state) => state.questionItemIdToBlockGroupIdListMap);
  const blockGroupIdToScoringCriteriaIdMap = useStore(usePaperDataStoreContext, (state) => state.blockGroupIdToScoringCriteriaIdMap);
  const scoringCriteriaIdToScoringCriteriaMap = useStore(usePaperDataStoreContext, (state) => state.scoringCriteriaIdToScoringCriteriaMap);
  //判断当前使用的页码是不是已有的
  const pageSize = (() => {
    for (let i = 0; i < PageSizeList.length; i++) {
      const element = PageSizeList[i];
      if (element.height === answerCard.height && element.width === answerCard.width) {
        return element.value;
      }
      return 'a4-96dpi';
    }
  })();
  const useToolbarDataStoreContext = useContext(ToolbarDataStoreContext);
  const showAnswerBox = useStore(useToolbarDataStoreContext, (state) => state.showAnswerBox);
  const setShowAnswerBox = useStore(useToolbarDataStoreContext, (state) => state.setShowAnswerBox);
  const setAnswerBoxVersion = useStore(useToolbarDataStoreContext, (state) => state.setAnswerBoxVersion);
  const pageBackground = useStore(useToolbarDataStoreContext, (state) => state.pageBackground);
  const setPageBackground = useStore(useToolbarDataStoreContext, (state) => state.setPageBackground);
  const showPageBackground = useStore(useToolbarDataStoreContext, (state) => state.showPageBackground);
  const setShowPageBackground = useStore(useToolbarDataStoreContext, (state) => state.setShowPageBackground);
  const printFunc = useStore(useToolbarDataStoreContext, (state) => state.printFunc);

  //计算题目总数和总分数
  let totalScore = 0;
  questionItemIdList.forEach((questionItemId) => {
    const blockGroupIdList = questionItemIdToBlockGroupIdListMap.get(questionItemId);
    if (!blockGroupIdList) {
      return;
    }
    blockGroupIdList.forEach((blockGroupId) => {
      const scoringCriteriaId = blockGroupIdToScoringCriteriaIdMap.get(blockGroupId);
      if (!scoringCriteriaId) {
        return;
      }
      const scoringCriteria = scoringCriteriaIdToScoringCriteriaMap.get(scoringCriteriaId);
      if (!scoringCriteria) {
        return;
      }
      totalScore += scoringCriteria.score ?? 0;
    });
  });
  
  return (
    <>
      <div className="tool-bar-main" key={answerCard.id}>
        <ToolbarItem title="尺寸：">
          <QuestionCardUI.Select
            placeholder="请选择尺寸"
            defaultValue={pageSize}
            options={PageSizeList.map((item) => {
              return { value: item.value, label: <span>{item.label}</span> };
            })}
            onValueChange={(value) => {
              let newSize = PageSizeList.find((it) => it.value === value);
              if (newSize === undefined) {
                return;
              }
              setAnswerCard({
                ...answerCard,
                width: newSize.width,
                height: newSize.height,
              });
            }}
          />
        </ToolbarItem>
        <ToolbarItem title="纸张方向：">
          <QuestionCardUI.RadioGroup
            onValueChange={(v) => {
              setAnswerCard({ ...answerCard, page_orientation_is_vertical: v.toLowerCase() === 'true' });
            }}
            defaultValue={answerCard.page_orientation_is_vertical ? 'true' : 'false'}
            options={[
              {
                value: 'true',
                label: (
                  <>
                    <div>纵向</div>
                  </>
                ),
              },
              {
                value: 'false',
                label: (
                  <>
                    <div>横向</div>
                  </>
                ),
              },
            ]}
          />
        </ToolbarItem>
        <ToolbarItem title="分栏数量：">
          <QuestionCardUI.InputNumber
            className="flex-grow"
            suffix="栏每页"
            defaultValue={answerCard.bucket_size}
            min={1}
            style={{ flexGrow: 1 }}
            onValueChange={(v) => setAnswerCard({ ...answerCard, bucket_size: parseInt(String(v ?? 1)) })}
          />
        </ToolbarItem>
        <ToolbarItem title="页码：">
          <QuestionCardUI.Switch
            checkedChildren="显示"
            unCheckedChildren="隐藏"
            defaultChecked={answerCard.show_page_index}
            onCheckedChange={(v) => {
              setAnswerCard({ ...answerCard, show_page_index: v });
            }}
          />
        </ToolbarItem>
        <ToolbarItem title="定位点：">
          <QuestionCardUI.Switch
            checkedChildren="显示"
            unCheckedChildren="隐藏"
            defaultChecked={answerCard.show_pos_point}
            onCheckedChange={(v) => setAnswerCard({ ...answerCard, show_pos_point: v })}
          />
        </ToolbarItem>
        <ToolbarItem title="作答区预览：">
          <QuestionCardUI.Switch
            checkedChildren="显示"
            unCheckedChildren="隐藏"
            defaultChecked={showAnswerBox}
            onCheckedChange={(v) => {
              setShowAnswerBox(v);
              setAnswerBoxVersion(Date.now().toString());
            }}
          />
        </ToolbarItem>
        <ToolbarItem title="页面上边距：">
          <QuestionCardUI.InputNumber className="flex-grow" suffix="像素" defaultValue={answerCard.y} min={0} onValueChange={(v) => setAnswerCard({ ...answerCard, y: parseInt(String(v ?? 0)) })} />
        </ToolbarItem>
        <ToolbarItem title="页面下边距：">
          <QuestionCardUI.InputNumber
            className="flex-grow"
            suffix="像素"
            defaultValue={answerCard.bottom}
            min={0}
            onValueChange={(v) => setAnswerCard({ ...answerCard, bottom: parseInt(String(v ?? 0)) })}
          />
        </ToolbarItem>
        <ToolbarItem title="页面左边距：">
          <QuestionCardUI.InputNumber className="flex-grow" suffix="像素" defaultValue={answerCard.x} min={0} onValueChange={(v) => setAnswerCard({ ...answerCard, x: parseInt(String(v ?? 0)) })} />
        </ToolbarItem>
        <ToolbarItem title="页面右边距：">
          <QuestionCardUI.InputNumber
            className="flex-grow"
            suffix="像素"
            defaultValue={answerCard.right}
            min={0}
            onValueChange={(v) => setAnswerCard({ ...answerCard, right: parseInt(String(v ?? 0)) })}
          />
        </ToolbarItem>
        <ToolbarItem title="数据操作：">
          <QuestionCardUI.Button
            onClick={() => {
              console.log(getPaperData());
            }}
            disabled={!isReadyToPrint}
          >
            打印数据
          </QuestionCardUI.Button>
          <QuestionCardUI.Button buttonType="primary" onClick={printFunc} disabled={!isReadyToPrint}>
            打印页面
          </QuestionCardUI.Button>
        </ToolbarItem>
        <ToolbarItem title="当前总分：">
          <QuestionCardUI.InputNumber className="flex-grow" suffix="分" value={`${totalScore}`} disabled />
        </ToolbarItem>
        <ToolbarItem title="显示页面背景">
          <QuestionCardUI.Switch checkedChildren="显示" unCheckedChildren="隐藏" defaultChecked={showPageBackground} onCheckedChange={(v) => setShowPageBackground(v)} />
        </ToolbarItem>
        <ToolbarItem title="页面背景图">
          <QuestionCardUI.Textarea
            autoSize={{ minRows: 1, maxRows: 6 }}
            defaultValue={pageBackground}
            onChange={(v) => {
              setPageBackground(v.target.value);
            }}
          />
        </ToolbarItem>
        <ToolbarItem title="新增题目">
          <QuestionCardUI.Button onClick={() => addQuestion()}>新增到最后一题</QuestionCardUI.Button>
        </ToolbarItem>
      </div>
    </>
  );
}

export interface ToolbarItemProps extends PropsWithChildren {
  title: string;
}

export function ToolbarItem({ title, children }: ToolbarItemProps) {
  return (
    <div className="compontent-line flex-center-gap10">
      <div className="label">{title}</div>
      {children}
    </div>
  );
}
