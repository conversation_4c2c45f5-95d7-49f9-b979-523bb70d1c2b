import React, { useCallback, useEffect, useState } from 'react';
import '../global.scss';
import './input-number.component.scss';
export interface Props extends React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
  suffix?: string;
  onValueChange?: (v: number | undefined) => void;
  defaultValue?: string | number | readonly string[] | undefined;
}
export default function QuestionCardInputNumber({ suffix, className, min, max, defaultValue, onValueChange, onChange,value,disabled, ...props }: Props) {
  const [newValue, setNewValue] = useState(defaultValue ?? value);
  const onChangeAction = useCallback(
    (value?: string | number) => {
      let newValue = 0;
      if (value === undefined) {
        onValueChange && onValueChange(undefined);
      } else {
        newValue = parseFloat(value.toString());
        if (isNaN(newValue)) {
          onValueChange && onValueChange(undefined);
          newValue = 0;
        } else {
          newValue = min !== undefined ? Math.max(parseFloat(String(min)), newValue) : newValue;
          newValue = max !== undefined ? Math.min(parseFloat(String(max)), newValue) : newValue;
          onValueChange && onValueChange(newValue);
        }
      }
      setNewValue(newValue);
    },
    [max, min, onValueChange],
  );
  useEffect(() => {
    setNewValue(value)
  },[value])
  const onWheelAction = useCallback(
    (e: React.WheelEvent<HTMLInputElement>) => {
      //当前值
      const currentValue = parseFloat(e.currentTarget.value);
      if (isNaN(currentValue)) {
        onChangeAction(defaultValue?.toString());
      } else {
        const step = props.step ? parseFloat(props.step.toString()) : 1;
        const direction = e.deltaY > 0 ? -1 : 1;
        onChangeAction(currentValue + step * direction);
      }
    },
    [onChangeAction, defaultValue, props.step],
  );
  return (
    <div className={`${className ?? ''} input-number-root`}>
      <div className="input-number-contain flex-center">
        <input
          className="input flex-grow"
          type="number"
        disabled={disabled}
          {...props}
          value={newValue}
          min={min}
          max={max}
          onChange={(v) => {
            onChange && onChange(v);
            onChangeAction(v.target.value);
          }}
          onWheel={e => {
            !disabled && onWheelAction(e)
          }}
        />
        {suffix && <div className="suffix">{suffix}</div>}
      </div>
      <div className="scroll-hidden"></div>
    </div>
  );
}
