@use '../global.scss' as base;

.input-main {
  @extend .unset;
  @extend .border-default;
  @extend .hover-default;
  width: 120px;
  line-height: 30px;
  box-sizing: border-box;

  .input {
    @extend .unset;
    @extend .hover-default;
    @extend .disabled;
    padding: 0 8px;
    height: 30px;
    line-height: 30px;
    border-radius: base.$border-radius;
    width: 0;
    flex-grow: 1;
    cursor: pointer;
  }

  .suffix {
    white-space: nowrap;
    padding: 0 5px;
  }
}
